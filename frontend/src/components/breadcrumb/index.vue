<template>
  <a-breadcrumb class="container-breadcrumb">
    <a-breadcrumb-item>
      <icon-apps />
    </a-breadcrumb-item>
    <a-breadcrumb-item v-for="item in items" :key="item">
      {{ getBreadcrumbTitle(item) }}
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
  import { PropType } from 'vue';

  // 面包屑标题映射
  const getBreadcrumbTitle = (locale: string): string => {
    const breadcrumbTitles: Record<string, string> = {
      'menu.dashboard': '仪表盘',
      'menu.dashboard.workplace': '工作台',
      'menu.dashboard.monitor': '实时监控',
      'menu.visualization': '数据可视化',
      'menu.visualization.dataAnalysis': '数据分析',
      'menu.visualization.multiDimensionDataAnalysis': '多维数据分析',
      'menu.list': '列表页',
      'menu.list.searchTable': '查询表格',
      'menu.list.cardList': '卡片列表',
      'menu.form': '表单页',
      'menu.form.step': '分步表单',
      'menu.form.group': '分组表单',
      'menu.profile': '详情页',
      'menu.profile.basic': '基础详情页',
      'menu.result': '结果页',
      'menu.result.success': '成功页',
      'menu.result.error': '失败页',
      'menu.exception': '异常页',
      'menu.exception.403': '403',
      'menu.exception.404': '404',
      'menu.exception.500': '500',
      'menu.user': '个人中心',
      'menu.user.info': '用户信息',
      'menu.user.setting': '用户设置',
      'menu.arcoWebsite': 'Arco Design',
      'menu.faq': '常见问题',
    };
    return breadcrumbTitles[locale] || locale;
  };

  defineProps({
    items: {
      type: Array as PropType<string[]>,
      default() {
        return [];
      },
    },
  });
</script>

<style scoped lang="less">
  .container-breadcrumb {
    margin: 16px 0;
    :deep(.arco-breadcrumb-item) {
      color: rgb(var(--gray-6));
      &:last-child {
        color: rgb(var(--gray-8));
      }
    }
  }
</style>
