<script lang="tsx">
  import { defineComponent, ref, h, compile, computed } from 'vue';
  import { useRoute, useRouter, RouteRecordRaw } from 'vue-router';
  import type { RouteMeta } from 'vue-router';
  import { useAppStore } from '@/store';
  import { listenerRouteChange } from '@/utils/route-listener';
  import { openWindow, regexUrl } from '@/utils';
  import useMenuTree from './use-menu-tree';

  // 菜单标题映射
  const getMenuTitle = (locale: string): string => {
    const menuTitles: Record<string, string> = {
      'menu.dashboard': '仪表盘',
      'menu.dashboard.workplace': '工作台',
      'menu.dashboard.monitor': '实时监控',
      'menu.visualization': '数据可视化',
      'menu.visualization.dataAnalysis': '数据分析',
      'menu.visualization.multiDimensionDataAnalysis': '多维数据分析',
      'menu.content': '内容管理',
      'menu.content.benchmarking': '对标帐号',
      'menu.content.cooperate': '合作笔记',
      'menu.content.promotion': '推广笔记',
      'menu.content.material': '素材库',
      'menu.content.searchWord': '笔记库',
      'menu.resource': '资源管理',
      'menu.resource.account': '帐号',
      'menu.resource.device': '设备',
      'menu.resource.mobileCard': '手机卡',
      'menu.organizational': '组织管理',
      'menu.organizational.departments': '部门',
      'menu.organizational.role': '角色',
      'menu.organizational.userGroup': '用户组',
      'menu.settings': '设置',
      'menu.settings.accountCookie': '帐号cookie',
      'menu.settings.ipPool': 'ip池',
      'menu.settings.tags': '标签',
      'menu.list': '列表页',
      'menu.list.searchTable': '查询表格',
      'menu.list.cardList': '卡片列表',
      'menu.form': '表单页',
      'menu.form.step': '分步表单',
      'menu.form.group': '分组表单',
      'menu.profile': '详情页',
      'menu.profile.basic': '基础详情页',
      'menu.result': '结果页',
      'menu.result.success': '成功页',
      'menu.result.error': '失败页',
      'menu.exception': '异常页',
      'menu.exception.403': '403',
      'menu.exception.404': '404',
      'menu.exception.500': '500',
      'menu.user': '个人中心',
      'menu.user.info': '用户信息',
      'menu.user.setting': '用户设置',
      'menu.arcoWebsite': 'Arco Design',
      'menu.faq': '常见问题',
    };
    return menuTitles[locale] || locale;
  };

  export default defineComponent({
    emit: ['collapse'],
    setup() {
      const appStore = useAppStore();
      const router = useRouter();
      const route = useRoute();
      const { menuTree } = useMenuTree();
      const collapsed = computed({
        get() {
          if (appStore.device === 'desktop') return appStore.menuCollapse;
          return false;
        },
        set(value: boolean) {
          appStore.updateSettings({ menuCollapse: value });
        },
      });

      const topMenu = computed(() => appStore.topMenu);
      const openKeys = ref<string[]>([]);
      const selectedKey = ref<string[]>([]);

      const goto = (item: RouteRecordRaw) => {
        // Open external link
        if (regexUrl.test(item.path)) {
          openWindow(item.path);
          selectedKey.value = [item.name as string];
          return;
        }
        // Eliminate external link side effects
        const { hideInMenu, activeMenu } = item.meta as RouteMeta;
        if (route.name === item.name && !hideInMenu && !activeMenu) {
          selectedKey.value = [item.name as string];
          return;
        }
        // Trigger router change
        router.push({
          name: item.name,
        });
      };
      const findMenuOpenKeys = (target: string) => {
        const result: string[] = [];
        let isFind = false;
        const backtrack = (item: RouteRecordRaw, keys: string[]) => {
          if (item.name === target) {
            isFind = true;
            result.push(...keys);
            return;
          }
          if (item.children?.length) {
            item.children.forEach((el) => {
              backtrack(el, [...keys, el.name as string]);
            });
          }
        };
        menuTree.value.forEach((el: RouteRecordRaw) => {
          if (isFind) return; // Performance optimization
          backtrack(el, [el.name as string]);
        });
        return result;
      };
      listenerRouteChange((newRoute) => {
        const { requiresAuth, activeMenu, hideInMenu } = newRoute.meta;
        if (requiresAuth && (!hideInMenu || activeMenu)) {
          const menuOpenKeys = findMenuOpenKeys(
            (activeMenu || newRoute.name) as string
          );

          const keySet = new Set([...menuOpenKeys, ...openKeys.value]);
          openKeys.value = [...keySet];

          selectedKey.value = [
            activeMenu || menuOpenKeys[menuOpenKeys.length - 1],
          ];
        }
      }, true);
      const setCollapse = (val: boolean) => {
        if (appStore.device === 'desktop')
          appStore.updateSettings({ menuCollapse: val });
      };

      const renderSubMenu = () => {
        function travel(_route: RouteRecordRaw[], nodes = []) {
          if (_route) {
            _route.forEach((element) => {
              // This is demo, modify nodes as needed
              const icon = element?.meta?.icon
                ? () => h(compile(`<${element?.meta?.icon}/>`))
                : null;
              const node =
                element?.children && element?.children.length !== 0 ? (
                  <a-sub-menu
                    key={element?.name}
                    v-slots={{
                      icon,
                      title: () => getMenuTitle(element?.meta?.locale || ''),
                    }}
                  >
                    {travel(element?.children)}
                  </a-sub-menu>
                ) : (
                  <a-menu-item
                    key={element?.name}
                    v-slots={{ icon }}
                    onClick={() => goto(element)}
                  >
                    {getMenuTitle(element?.meta?.locale || '')}
                  </a-menu-item>
                );
              nodes.push(node as never);
            });
          }
          return nodes;
        }
        return travel(menuTree.value);
      };

      return () => (
        <a-menu
          mode={topMenu.value ? 'horizontal' : 'vertical'}
          v-model:collapsed={collapsed.value}
          v-model:open-keys={openKeys.value}
          show-collapse-button={appStore.device !== 'mobile'}
          auto-open={false}
          selected-keys={selectedKey.value}
          auto-open-selected={true}
          level-indent={34}
          style="height: 100%;width:100%;"
          onCollapse={setCollapse}
        >
          {renderSubMenu()}
        </a-menu>
      );
    },
  });
</script>

<style lang="less" scoped>
  :deep(.arco-menu-inner) {
    .arco-menu-inline-header {
      display: flex;
      align-items: center;
    }
    .arco-icon {
      &:not(.arco-icon-down) {
        font-size: 18px;
      }
    }
  }
</style>
