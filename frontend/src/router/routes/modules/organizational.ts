import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ORGANIZATIONAL: AppRouteRecordRaw = {
  path: '/organizational',
  name: 'organizational',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.organizational',
    requiresAuth: true,
    icon: 'icon-user-group',
    order: 2,
  },
  children: [
    {
      path: 'departments',
      name: 'departments',
      component: () => import('@/views/organizational/departments/index.vue'),
      meta: {
        locale: 'menu.organizational.departments',
        requiresAuth: true,
        roles: ['*'],
      },
    },

    {
      path: 'role',
      name: 'role',
      component: () => import('@/views/organizational/role/index.vue'),
      meta: {
        locale: 'menu.organizational.role',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'userGroup',
      name: 'userGroup',
      component: () => import('@/views/organizational/userGroup/index.vue'),
      meta: {
        locale: 'menu.organizational.userGroup',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default ORGANIZATIONAL;
