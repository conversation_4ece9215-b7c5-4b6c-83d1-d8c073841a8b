import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SETTINGS: AppRouteRecordRaw = {
  path: '/settings',
  name: 'settings',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.settings',
    requiresAuth: true,
    icon: 'icon-settings',
    order: 2,
  },
  children: [
    {
      path: 'accountCookie',
      name: 'accountCookie',
      component: () => import('@/views/settings/accountCookie/index.vue'),
      meta: {
        locale: 'menu.settings.accountCookie',
        requiresAuth: true,
        roles: ['*'],
      },
    },

    {
      path: 'ipPool',
      name: 'ipPool',
      component: () => import('@/views/settings/ipPool/index.vue'),
      meta: {
        locale: 'menu.settings.ipPool',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'tags',
      name: 'tags',
      component: () => import('@/views/settings/tags/index.vue'),
      meta: {
        locale: 'menu.settings.tags',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default SETTINGS;
