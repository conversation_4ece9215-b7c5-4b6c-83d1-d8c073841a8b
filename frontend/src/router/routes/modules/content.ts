import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const CONTENT: AppRouteRecordRaw = {
  path: '/content',
  name: 'content',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.content',
    requiresAuth: true,
    icon: 'icon-folder',
    order: 2,
  },
  children: [
    {
      path: 'benchmarking',
      name: 'benchmarking',
      component: () => import('@/views/content/benchmarking/index.vue'),
      meta: {
        locale: 'menu.content.benchmarking',
        requiresAuth: true,
        roles: ['*'],
      },
    },

    {
      path: 'cooperate',
      name: 'cooperate',
      component: () => import('@/views/content/cooperate/index.vue'),
      meta: {
        locale: 'menu.content.cooperate',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'promotion',
      name: 'promotion',
      component: () => import('@/views/content/promotion/index.vue'),
      meta: {
        locale: 'menu.content.promotion',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'material',
      name: 'material',
      component: () => import('@/views/content/material/index.vue'),
      meta: {
        locale: 'menu.content.material',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'searchWord',
      name: 'searchWord',
      component: () => import('@/views/content/searchWord/index.vue'),
      meta: {
        locale: 'menu.content.searchWord',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default CONTENT;
