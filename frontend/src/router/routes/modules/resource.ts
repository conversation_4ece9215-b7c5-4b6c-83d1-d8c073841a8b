import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const CONTENT: AppRouteRecordRaw = {
  path: '/resource',
  name: 'resource',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.resource',
    requiresAuth: true,
    icon: 'icon-layers',
    order: 2,
  },
  children: [
    {
      path: 'account',
      name: 'account',
      component: () => import('@/views/resource/account/index.vue'),
      meta: {
        locale: 'menu.resource.account',
        requiresAuth: true,
        roles: ['*'],
      },
    },

    {
      path: 'device',
      name: 'device',
      component: () => import('@/views/resource/device/index.vue'),
      meta: {
        locale: 'menu.resource.device',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
    {
      path: 'mobileCard',
      name: 'mobileCard',
      component: () => import('@/views/resource/mobileCard/index.vue'),
      meta: {
        locale: 'menu.resource.mobileCard',
        requiresAuth: true,
        roles: ['admin'],
      },
    },
  ],
};

export default CONTENT;
