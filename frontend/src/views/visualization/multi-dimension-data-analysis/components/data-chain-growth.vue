<template>
  <div>
    <a-row :gutter="16">
      <a-col :span="6">
        <ChainItem
          :title="'留存趋势'"
          quota="retentionTrends"
          chart-type="line"
        />
      </a-col>
      <a-col :span="6">
        <ChainItem :title="'用户留存'" quota="userRetention" chart-type="bar" />
      </a-col>
      <a-col :span="6">
        <ChainItem
          :title="'内容消费趋势'"
          quota="contentConsumptionTrends"
          chart-type="line"
        />
      </a-col>
      <a-col :span="6">
        <ChainItem
          :title="'内容消费'"
          quota="contentConsumption"
          chart-type="bar"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import ChainItem from './chain-item.vue';
</script>
