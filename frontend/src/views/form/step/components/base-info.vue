<template>
  <a-form
    ref="formRef"
    :model="formData"
    class="form"
    :label-col-props="{ span: 6 }"
    :wrapper-col-props="{ span: 18 }"
  >
    <a-form-item
      field="activityName"
      :label="'活动名称'"
      :rules="[
        {
          required: true,
          message: '活动名称是必填项',
        },
        {
          match: /^[a-zA-Z0-9\u4e00-\u9fa5]{1,20}$/,
          message: '不符合活动名称命名规则',
        },
      ]"
    >
      <a-input
        v-model="formData.activityName"
        :placeholder="'请输入活动名称'"
      />
    </a-form-item>
    <a-form-item
      field="channelType"
      :label="'渠道类型'"
      :rules="[
        {
          required: true,
          message: '渠道类型是必选项',
        },
      ]"
    >
      <a-select v-model="formData.channelType" :placeholder="'请选择渠道类型'">
        <a-option>APP通用渠道</a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="promotionTime"
      :label="'推广时间'"
      :rules="[
        {
          required: true,
          message: '推广时间是必选项',
        },
      ]"
    >
      <a-range-picker v-model="formData.promotionTime" />
    </a-form-item>
    <a-form-item
      field="promoteLink"
      :label="'推广链接'"
      :rules="[
        {
          required: true,
          message: '推广链接是必填项',
        },
        {
          type: 'url',
          message: '不是合法的链接格式',
        },
      ]"
      row-class="keep-margin"
    >
      <a-input v-model="formData.promoteLink" :placeholder="'请输入推广链接'" />
      <template #help>
        <span>{{ '推广链接的用途' }}</span>
      </template>
    </a-form-item>
    <a-form-item>
      <a-button type="primary" @click="onNextClick">
        {{ '下一步' }}
      </a-button>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { BaseInfoModel } from '@/api/form';

  const emits = defineEmits(['changeStep']);
  const formRef = ref<FormInstance>();
  const formData = ref<BaseInfoModel>({
    activityName: '',
    channelType: '',
    promotionTime: [],
    promoteLink: 'https://arco.design',
  });

  const onNextClick = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      emits('changeStep', 'forward', { ...formData.value });
    }
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px;
    .keep-margin {
      margin-bottom: 20px;
    }
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 64px 0;
    background-color: var(--color-bg-2);
  }

  .steps {
    margin-bottom: 36px;
  }

  .form {
    width: 500px;
  }

  .form-content {
    padding: 8px 50px 0 30px;
  }
</style>
