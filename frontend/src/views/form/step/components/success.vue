<template>
  <div class="success-wrap">
    <a-result
      status="success"
      :title="'提交成功'"
      :subtitle="'提交结果页用于反馈一系列操作任务的处理结果'"
    />
    <a-space :size="16">
      <a-button key="view" type="primary">
        {{ '查看项目' }}
      </a-button>
      <a-button key="again" type="secondary" @click="oneMore">
        {{ '再提交一个' }}
      </a-button>
    </a-space>
    <div class="details-wrapper">
      <a-typography-title :heading="6" style="margin-top: 0">
        {{ '项目名称' }}
      </a-typography-title>
      <a-typography-paragraph style="margin-bottom: 0">
        {{ '项目的配置修改成功' }}
        <a-link href="link">{{ '查看项目' }}</a-link>
      </a-typography-paragraph>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const emits = defineEmits(['changeStep']);
  const oneMore = () => {
    emits('changeStep', 1);
  };
</script>

<style scoped lang="less">
  .success-wrap {
    text-align: center;
  }
  :deep(.arco-result) {
    padding-top: 0;
  }
  .details-wrapper {
    width: 895px;
    margin-top: 54px;
    padding: 20px;
    text-align: left;
    background-color: var(--color-fill-2);
  }
</style>
