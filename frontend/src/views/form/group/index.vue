<template>
  <div class="container">
    <Breadcrumb :items="['menu.form', 'menu.form.group']" />
    <a-form ref="formRef" layout="vertical" :model="formData">
      <a-space direction="vertical" :size="16">
        <a-card class="general-card">
          <template #title>
            {{ '视频' }}
          </template>
          <a-row :gutter="80">
            <a-col :span="8">
              <a-form-item :label="'视频模式'" field="video.mode">
                <a-select :placeholder="'请选择视频模式'">
                  <a-option value="custom">自定义</a-option>
                  <a-option value="mode1">模式1</a-option>
                  <a-option value="mode2">模式2</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="'采集分辨率'"
                field="video.acquisition.resolution"
              >
                <a-select :placeholder="'请选择采集分辨率'">
                  <a-option value="resolution1">分辨率1</a-option>
                  <a-option value="resolution2">分辨率2</a-option>
                  <a-option value="resolution3">分辨率3</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="'采集帧率'"
                field="video.acquisition.frameRate"
              >
                <a-input :placeholder="'请选择采集帧率'">
                  <template #append> fps </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="80">
            <a-col :span="8">
              <a-form-item
                :label="'编码分辨率'"
                field="video.encoding.resolution"
              >
                <a-select :placeholder="'请选择编码分辨率'">
                  <a-option value="resolution1">分辨率1</a-option>
                  <a-option value="resolution2">分辨率2</a-option>
                  <a-option value="resolution3">分辨率3</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="'最小码率'" field="video.encoding.rate.min">
                <a-input :placeholder="'请输入最小码率'" add-after="bps">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="'最大码率'" field="video.encoding.rate.max">
                <a-input :placeholder="'请输入最大码率'">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="80">
            <a-col :span="8">
              <a-form-item
                :label="'默认码率'"
                field="video.encoding.rate.default"
              >
                <a-input :placeholder="'请输入默认码率'">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="'编码帧率'" field="video.encoding.frameRate">
                <a-input :placeholder="'请选择编码帧率'">
                  <template #append> fps </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="'编码规格'" field="video.encoding.profile">
                <a-input :placeholder="'请选择编码规格'">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="general-card">
          <template #title>
            {{ '音频' }}
          </template>
          <a-row :gutter="80">
            <a-col :span="8">
              <a-form-item :label="'音频模式'" field="audio.mode">
                <a-select :placeholder="'请选择音频模式'">
                  <a-option value="custom">自定义</a-option>
                  <a-option value="mode1">模式1</a-option>
                  <a-option value="mode2">模式2</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="'采集声道'"
                field="audio.acquisition.channels"
              >
                <a-select :placeholder="'请选择采集声道'">
                  <a-option value="1">1</a-option>
                  <a-option value="2">2</a-option>
                  <a-option value="3">3</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="'编码声道'" field="audio.encoding.channels">
                <a-input :placeholder="'请选择编码声道'">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="80">
            <a-col :span="8">
              <a-form-item :label="'编码码率'" field="audio.encoding.rate">
                <a-input :placeholder="'请输入编码码率'">
                  <template #append> bps </template>
                </a-input>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item :label="'编码规格'" field="audio.encoding.profile">
                <a-input :placeholder="'请选择编码规格'">
                  <template #append> fps </template>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card class="general-card" :bordered="false">
          <template #title>
            {{ '参数描述' }}
          </template>
          <a-form-item :label="'参数描述'" field="audio.approvers">
            <a-textarea :placeholder="'请输入参数描述'" />
          </a-form-item>
        </a-card>
      </a-space>
      <div class="actions">
        <a-space>
          <a-button>
            {{ '重置' }}
          </a-button>
          <a-button type="primary" :loading="loading" @click="onSubmitClick">
            {{ '提交' }}
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import useLoading from '@/hooks/loading';

  const formData = ref({});
  const formRef = ref<FormInstance>();
  const { loading, setLoading } = useLoading();
  const onSubmitClick = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      setLoading(true);
    }
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };
</script>

<script lang="ts">
  export default {
    name: 'Group',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 40px 20px;
    overflow: hidden;
  }

  .actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 14px 20px 14px 0;
    background: var(--color-bg-2);
    text-align: right;
  }
</style>
