<template>
  <div class="container">
    <Breadcrumb :items="['资源管理', '账号管理']" />
    <a-card class="general-card" :title="'账号管理'">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item field="accountId" :label="'账号'">
                  <a-input
                    v-model="formModel.accountId"
                    :placeholder="'请输入账号ID或昵称'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="accountName" :label="'账号名称'">
                  <a-input
                    v-model="formModel.accountName"
                    :placeholder="'请输入账号名称'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="accountType" :label="'帐号类型'">
                  <a-select
                    v-model="formModel.accountType"
                    :options="accountTypeOptions"
                    :placeholder="'全部'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="platform" :label="'平台'">
                  <a-select
                    v-model="formModel.platform"
                    :options="platformOptions"
                    :placeholder="'全部'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="department" :label="'部门'">
                  <a-select
                    v-model="formModel.department"
                    :options="departmentOptions"
                    :placeholder="'全部'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="department" :label="'运营人'">
                  <a-select
                    v-model="formModel.operator"
                    :options="operatorOptions"
                    :placeholder="'全部'"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="createdTime" :label="'注册日期'">
                  <a-range-picker
                    v-model="formModel.createdTime"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item field="status" :label="'状态'">
                  <a-select
                    v-model="formModel.status"
                    :options="statusOptions"
                    :placeholder="'全部'"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              {{ '查询' }}
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              {{ '重置' }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="24">
          <a-space>
            <a-button type="primary">
              <template #icon>
                <icon-plus />
              </template>
              {{ '新建账号' }}
            </a-button>
            <a-upload action="/">
              <template #upload-button>
                <a-button>
                  {{ '批量设置运营' }}
                </a-button>
              </template>
            </a-upload>
            <a-tooltip :content="'刷新'">
            <div class="action-icon" @click="search"
              ><icon-refresh size="18"
            /></div>
          </a-tooltip>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :bordered="false"
        :row-selection="rowSelection"
        :size="size"
        @page-change="onPageChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #platform="{ record }">
          <a-space>
            {{
              platformOptions.find((opt) => opt.value === record.platform)
                ?.label || record.platform
            }}
          </a-space>
        </template>
        <template #accountType="{ record }">
          <a-space>
            {{
              accountTypeOptions.find((opt) => opt.value === record.accountType)
                ?.label || record.accountType
            }}
          </a-space>
        </template>
        <template #department="{ record }">
          <a-space>
            {{
              departmentOptions.find((opt) => opt.value === record.department)
                ?.label || record.department
            }}
          </a-space>
        </template>
        <template #operator="{ record }">
          <a-space>
            {{
              operatorOptions.find((opt) => opt.value === record.operator)
                ?.label || record.operator
            }}
          </a-space>
        </template>
        <template #loginDevice="{ record }"> 
          <a-space>
            {{
              loginDeviceOptions.find((opt) => opt.value === record.loginDevice)
                ?.label || record.loginDevice
            }}
          </a-space>
        </template>
        <template #status="{ record }">
          <span v-if="record.status === 'inactive'" class="circle"></span>
          <span v-else class="circle pass"></span>
          {{
            statusOptions.find((opt) => opt.value === record.status)?.label ||
            record.status
          }}
        </template>
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="small">
              {{ '编辑' }}
            </a-button>
            <a-button
              type="text"
              size="small"
              status="danger"
              @click="handleDelete(record)"
            >
              {{ '删除' }}
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';

  type SizeProps = 'mini' | 'small' | 'medium' | 'large';

  interface AccountRecord {
    id: string;
    accountId: string;
    accountName: string;
    accountType: string;
    platform: string;
    department: string;
    operator: string;
    loginDevice: string;
    createdTime: string;
    status: string;
  }

  const generateFormModel = () => {
    return {
      accountId: '',
      accountName: '',
      accountType: '',
      platform: '',
      department: '',
      operator: '',
      loginDevice: '',
      createdTime: [],
      status: '',
    };
  };

  const loading = ref(false);
  const renderData = ref<AccountRecord[]>([]);
  const formModel = ref(generateFormModel());
  const size = ref<SizeProps>('medium');
    const rowSelection = reactive({
      type: 'checkbox',
      showCheckedAll: true,
      onlyCurrent: false,
    });

  const basePagination = {
    current: 1,
    pageSize: 20,
  };

  const pagination = reactive({
    ...basePagination,
    total: 0,
  });

  const columns = computed(() => [
    {
      title: 'id',
      dataIndex: 'index',
      slotName: 'index',
      width: 60,
    },
    {
      title: '昵称',
      dataIndex: 'accountName',
      width: 118,
      fixed: 'left',
    },
    {
      title: '账号ID',
      dataIndex: 'accountId',
      width: 120,
    },
    {
      title: '帐号类型',
      dataIndex: 'accountType',
      width: 120,
    },
    {
      title: '平台',
      dataIndex: 'platform',
      slotName: 'platform',
      width: 120,
    },
    {
      title: '部门',
      dataIndex: 'department',
      slotName: 'department',
      width: 100,
    },
    {
      title: '运营人',
      dataIndex: 'operator',
      slotName: 'operator',
      width: 100,
    },
    {
      title: '登录设备',
      dataIndex: 'loginDevice',
      slotName: 'loginDevice',
      width: 100,
    },
    {
      title: '注册日期',
      dataIndex: 'createdTime',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      slotName: 'status',
      width: 80,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      width: 120,
      fixed: 'right',
    },
  ]);

  const platformOptions = computed(() => [
    { label: '微信', value: 'wechat' },
    { label: '微博', value: 'weibo' },
    { label: '抖音', value: 'douyin' },
    { label: '小红书', value: 'xiaohongshu' },
  ]);

  const departmentOptions = computed(() => [
    { label: '运营部', value: 'department1' },
    { label: '直播部', value: 'department2' },
    { label: '其他', value: 'department3' },
  ]);

  const operatorOptions = computed(() => [
    { label: '刘志强', value: 'operator1' },
    { label: '张在峰', value: 'operator2' },
    { label: '王强', value: 'operator3' },
  ]);

  const loginDeviceOptions = computed(() => [
    { label: '9527', value: 'device1' },
    { label: '5865', value: 'device2' },
    { label: '设备3', value: 'device3' },
  ]);

  const accountTypeOptions = computed(() => [
    { label: '人设号', value: 'personality' },
    { label: '互动号', value: 'interaction' },
    { label: '认证号', value: 'certified' },
    { label: '其他', value: 'other' },
  ]);

  const statusOptions = computed(() => [
    { label: '正常', value: 'active' },
    { label: '停用', value: 'inactive' },
  ]);

  // Generate mock data
  const generateMockData = (): AccountRecord[] => {
    const platforms = ['wechat', 'weibo', 'douyin', 'xiaohongshu'];
    const statuses = ['active', 'inactive'];
    const accountTypes = ['personality', 'interaction', 'certified', 'other'];
    const departments = ['department1', 'department2', 'department3'];
    const operators = ['operator1', 'operator2', 'operator3'];
    const loginDevices = ['device1', 'device2', 'device3'];

    return Array.from({ length: 50 }, (_, index) => ({
      id: `account_${index + 1}`,
      accountId: `ACC${String(index + 1).padStart(6, '0')}`,
      accountName: `账号名称${index + 1}`,
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      accountType: accountTypes[Math.floor(Math.random() * accountTypes.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      operator: operators[Math.floor(Math.random() * operators.length)],
      loginDevice: loginDevices[Math.floor(Math.random() * loginDevices.length)],
      followerCount: Math.floor(Math.random() * 1000000) + 1000,
      createdTime: new Date(
        Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
      )
        .toISOString()
        .split('T')[0],
      status: statuses[Math.floor(Math.random() * statuses.length)],
    }));
  };

  const mockData = generateMockData();

  const fetchData = async (params = { current: 1, pageSize: 20 }) => {
    loading.value = true;

    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });

    try {
      let filteredData = mockData;

      if (formModel.value.accountId) {
        filteredData = filteredData.filter((item) =>
          item.accountId
            .toLowerCase()
            .includes(formModel.value.accountId.toLowerCase())
        );
      }

      if (formModel.value.accountName) {
        filteredData = filteredData.filter((item) =>
          item.accountName
            .toLowerCase()
            .includes(formModel.value.accountName.toLowerCase())
        );
      }

      if (formModel.value.platform) {
        filteredData = filteredData.filter(
          (item) => item.platform === formModel.value.platform
        );
      }

      if (formModel.value.accountType) {
        filteredData = filteredData.filter(
          (item) => item.accountType === formModel.value.accountType
        );
      }

      if (formModel.value.department) {
        filteredData = filteredData.filter(
          (item) => item.department === formModel.value.department
        );
      }

      if (formModel.value.operator) {
        filteredData = filteredData.filter(
          (item) => item.operator === formModel.value.operator
        );
      }

      if (formModel.value.status) {
        filteredData = filteredData.filter(
          (item) => item.status === formModel.value.status
        );
      }

      const start = (params.current - 1) * params.pageSize;
      const end = start + params.pageSize;
      const paginatedData = filteredData.slice(start, end);

      renderData.value = paginatedData;
      pagination.current = params.current;
      pagination.total = filteredData.length;
    } catch (err) {
      Message.error('数据获取失败');
    } finally {
      loading.value = false;
    }
  };

  const search = () => {
    fetchData({ ...basePagination });
  };

  const onPageChange = (current: number) => {
    fetchData({ ...basePagination, current });
  };

  const reset = () => {
    formModel.value = generateFormModel();
    fetchData();
  };

  const handleDelete = (record: AccountRecord) => {
    Message.info(`删除账号: ${record.accountName}`);
  };

  // Load initial data
  fetchData();
</script>

<script lang="ts">
  export default {
    name: 'AccountManagement',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
  }

  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }

  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }

  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #f53f3f;
    margin-right: 8px;
  }

  .circle.pass {
    background-color: #00b42a;
  }
</style>
