<template>
  <div class="container">
    <Breadcrumb :items="['menu.result', 'menu.result.success']" />
    <div class="wrapper">
      <a-result
        class="result"
        status="success"
        :title="'提交成功'"
        :subtitle="'提交结果页用于反馈一系列操作任务的处理结果'"
      >
        <template #extra>
          <a-space class="operation-wrap" :size="16">
            <a-button key="again" type="secondary">
              {{ '打印' }}
            </a-button>
            <a-button key="back" type="primary">
              {{ '项目列表' }}
            </a-button>
          </a-space>
        </template>
      </a-result>

      <div class="steps-wrapper">
        <a-typography-paragraph bold>{{ '进度' }}</a-typography-paragraph>
        <a-steps type="dot" :current="2">
          <a-step :title="'提交申请'" description="2020/10/10 14:00:39" />
          <a-step :title="'负责人审批'" :description="'处理中'" />
          <a-step :title="'采购凭证申请'" :description="'等待中'" />
          <a-step :title="'安全测试'" :description="'等待中'" />
          <a-step :title="'已上线'" :description="'等待中'" />
        </a-steps>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<script lang="ts">
  export default {
    name: 'Success',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
  }
  .wrapper {
    padding: 24px 150px;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }

  .result {
    margin: 150px 0 0 0;
  }

  .operation-wrap {
    margin-bottom: 40px;
    text-align: center;
  }

  .steps-wrapper {
    width: 100%;
    min-width: fit-content;
    margin-bottom: 150px;
    padding: 20px;
    background-color: rgb(var(--gray-1));
  }
</style>

<style lang="less" scoped>
  .mobile {
    .wrapper {
      padding: 24px 10px;
      .steps-wrapper {
        transform: scale(0.8);
      }
    }
  }
</style>
