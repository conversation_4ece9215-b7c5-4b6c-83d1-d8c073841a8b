import cn from './zh-CN';

// 简化的 t 函数，直接从中文语言包获取文本
export function t(key: string): string {
  const keys = key.split('.');
  let result: any = cn;

  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < keys.length; i++) {
    const k = keys[i];
    result = result?.[k];
    if (result === undefined) {
      return key;
    }
  }

  return typeof result === 'string' ? result : key;
}

// 为了兼容现有代码，导出一个空对象作为默认导出
export default {
  global: {
    t,
  },
};
